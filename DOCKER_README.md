# Docker Setup for FxD Partner ERP

This document provides instructions for building and running the FxD Partner ERP application using Docker.

## Prerequisites

- Docker installed on your system
- Docker Compose installed on your system
- Git (to clone the repository)

## Project Structure

```
FXD_ERP/
├── FxDPartnerERP/          # Frontend React/Vite application
│   ├── Dockerfile
│   └── .dockerignore
├── FxDPartnerERPBackend/   # Backend Node.js/Express application
│   ├── Dockerfile
│   └── .dockerignore
├── docker-compose.yml      # Docker Compose configuration
└── DOCKER_README.md       # This file
```

## Quick Start

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd FXD_ERP
```

### 2. Environment Configuration

#### Backend Environment
Create a `.env` file in the `FxDPartnerERPBackend` directory:
```bash
cp FxDPartnerERPBackend/.env.example FxDPartnerERPBackend/.env
```

Edit the `.env` file with your configuration:
```env
NODE_ENV=production
DB_HOST=database
DB_PORT=3306
DB_NAME=fxd_erp
DB_USER=fxd_user
DB_PASSWORD=fxd_password
JWT_SECRET=your-super-secret-jwt-key-here
PORT=3000
```

#### Frontend Environment (Optional)
Create a `.env` file in the `FxDPartnerERP` directory if needed:
```bash
# Create .env file for frontend if needed
touch FxDPartnerERP/.env
```

### 3. Build and Run with Docker Compose

#### Start all services:
```bash
docker-compose up -d
```

#### View logs:
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f frontend
docker-compose logs -f backend
docker-compose logs -f database
```

#### Stop all services:
```bash
docker-compose down
```

#### Stop and remove volumes (WARNING: This will delete database data):
```bash
docker-compose down -v
```

## Individual Service Commands

### Build Individual Services

#### Frontend:
```bash
docker build -t fxd-erp-frontend ./FxDPartnerERP
```

#### Backend:
```bash
docker build -t fxd-erp-backend ./FxDPartnerERPBackend
```

### Run Individual Containers

#### Frontend (after building):
```bash
docker run -p 80:80 fxd-erp-frontend
```

#### Backend (after building):
```bash
docker run -p 3000:3000 --env-file FxDPartnerERPBackend/.env fxd-erp-backend
```

## Service URLs

After starting the services, you can access:

- **Frontend**: http://localhost (port 80)
- **Backend API**: http://localhost:3000
- **Database**: localhost:3306 (for external connections)

## Understanding EXPOSE Ports

The `EXPOSE` instruction in Dockerfiles serves as documentation and tells Docker which ports the container will listen on at runtime:

- **Frontend EXPOSE 80**: The Nginx server inside the container listens on port 80 (standard HTTP port)
- **Backend EXPOSE 3000**: The Node.js Express server listens on port 3000
- **Database EXPOSE 3306**: MySQL server listens on port 3306 (standard MySQL port)

**Important**: `EXPOSE` doesn't actually publish the ports - it's just documentation. The actual port mapping happens in docker-compose.yml with the `ports` section:

```yaml
ports:
  - "host_port:container_port"
```

For example:
- `"80:80"` maps host port 80 to container port 80
- `"3000:3000"` maps host port 3000 to container port 3000
- `"3306:3306"` maps host port 3306 to container port 3306

## Database Setup

The MySQL database will be automatically initialized with:
- Database: `fxd_erp`
- User: `fxd_user`
- Password: `fxd_password`
- Root Password: `rootpassword`

### Automatic Migrations

The backend Docker container is configured to automatically run database migrations when it starts up. This happens through a startup script that:

1. Runs `npm run migrate` to apply any pending database migrations
2. Then starts the application with `npm start`

This ensures your database schema is always up-to-date when the container starts.

### Manual Migration Commands

If you need to run database migrations manually:

```bash
# Access the backend container
docker-compose exec backend bash

# Run migrations manually
npm run migrate

# Or run migrations without entering the container
docker-compose exec backend npm run migrate
```

### Migration Process Flow

When you start the services with `docker-compose up`:

1. **Database container** starts first and initializes MySQL
2. **Backend container** waits for database (due to `depends_on`)
3. **Backend startup script** runs migrations automatically
4. **Backend application** starts serving API requests
5. **Frontend container** starts and serves the web application

## Development vs Production

### Development Mode
For development, you might want to use volume mounts for live code reloading:

```yaml
# Add to docker-compose.yml under backend service
volumes:
  - ./FxDPartnerERPBackend:/app
  - /app/node_modules
```

### Production Mode
The current setup is optimized for production with:
- Multi-stage builds for the frontend
- Optimized Docker images
- Security best practices (non-root user for backend)
- Health checks

## Troubleshooting

### Common Issues

1. **Port conflicts**: If ports 80, 3000, or 3306 are already in use, modify the port mappings in `docker-compose.yml`

2. **Database connection issues**: Ensure the backend service waits for the database to be ready. The `depends_on` directive helps, but you might need to add a health check.

3. **Environment variables**: Make sure all required environment variables are set in the `.env` files.

4. **Build failures**: Check the Docker build logs for specific error messages:
   ```bash
   docker-compose build --no-cache
   ```

### Useful Commands

```bash
# Rebuild and restart services
docker-compose up --build -d

# View container status
docker-compose ps

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend sh

# Remove all containers and images
docker-compose down --rmi all

# View resource usage
docker stats
```

## Security Considerations

1. **Change default passwords**: Update all default passwords in production
2. **Environment variables**: Never commit `.env` files with sensitive data
3. **Network security**: Consider using Docker secrets for sensitive data
4. **Database security**: Use strong passwords and consider network isolation

## Scaling

To scale services horizontally:

```bash
# Scale backend to 3 instances
docker-compose up --scale backend=3 -d
```

Note: You'll need to configure a load balancer for multiple backend instances.

## Backup and Restore

### Database Backup
```bash
docker-compose exec database mysqldump -u root -p fxd_erp > backup.sql
```

### Database Restore
```bash
docker-compose exec -T database mysql -u root -p fxd_erp < backup.sql
