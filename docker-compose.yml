services:
  # Database service
  database:
    image: mysql:8.0
    container_name: fxd-erp-database
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: fxd_erp
      MYSQL_USER: fxd_user
      MY<PERSON><PERSON>_PASSWORD: fxd_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - fxd-erp-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "fxd_user", "-pfxd_password"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

  # Backend service
  backend:
    build:
      context: ./FxDPartnerERPBackend
      dockerfile: Dockerfile
    container_name: fxd-erp-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    depends_on:
      database:
        condition: service_healthy
    env_file:
      - ./FxDPartnerERPBackend/.env
    environment:
      - DB_HOST=database
      - DB_PORT=3306
      - DB_USER=fxd_user
      - DB_PASSWORD=fxd_password
      - DB_NAME=fxd_erp
      - NODE_ENV=production
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - PORT=3001
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=admin123
      - ADMIN_JWT_SECRET=admin-jwt-secret-key-2024
    networks:
      - fxd-erp-network

  # Frontend service
  frontend:
    build:
      context: ./FxDPartnerERP
      dockerfile: Dockerfile
    container_name: fxd-erp-frontend
    restart: unless-stopped
    ports:
      - "5173:80"
    environment:
      - VITE_API_URL=http://localhost:3001
      - VITE_ADMIN_API_URL=http://localhost:3001
      - VITE_ORGANIZATION_ID=default-org-id
      - VITE_API_TIMEOUT=10000
      - VITE_APP_NAME=FxD Partner ERP
      - VITE_APP_VERSION=1.0.0
      - VITE_ENABLE_DEBUG=false
      - VITE_ENABLE_ANALYTICS=false
    depends_on:
      - backend
    networks:
      - fxd-erp-network

volumes:
  mysql_data:
    driver: local

networks:
  fxd-erp-network:
    driver: bridge
