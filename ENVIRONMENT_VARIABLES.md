# Environment Variables Configuration

This document outlines all the environment variables you need to configure for the FxD Partner ERP application.

## Frontend Environment Variables

Create a `.env` file in the `FxDPartnerERP/` directory with the following variables:

### Required Variables

```env
# API Configuration
VITE_API_URL=http://localhost:3000
VITE_ORGANIZATION_ID=default-org-id
VITE_API_TIMEOUT=10000
```

### Optional Variables

```env
# Application Configuration
VITE_APP_NAME=FxD Partner ERP
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=false
```

### Variable Descriptions

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `VITE_API_URL` | Backend API base URL | `http://localhost:3000` | Yes |
| `VITE_ORGANIZATION_ID` | Default organization identifier | `default-org-id` | Yes |
| `VITE_API_TIMEOUT` | API request timeout in milliseconds | `10000` | No |
| `VITE_APP_NAME` | Application display name | `FxD Partner ERP` | No |
| `VITE_APP_VERSION` | Application version | `1.0.0` | No |
| `VITE_ENABLE_DEBUG` | Enable debug mode | `false` | No |
| `VITE_ENABLE_ANALYTICS` | Enable analytics tracking | `false` | No |

## Backend Environment Variables

Create a `.env` file in the `FxDPartnerERPBackend/` directory with the following variables:

### Required Variables

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=fxd_erp
DB_USER=fxd_user
DB_PASSWORD=fxd_password

# Application Configuration
NODE_ENV=development
PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

### Optional Variables

```env
# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Logging
LOG_LEVEL=info

# Session Configuration
SESSION_SECRET=your-session-secret-key
```

### Variable Descriptions

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `DB_HOST` | Database host | `localhost` | Yes |
| `DB_PORT` | Database port | `3306` | Yes |
| `DB_NAME` | Database name | `fxd_erp` | Yes |
| `DB_USER` | Database username | `fxd_user` | Yes |
| `DB_PASSWORD` | Database password | `fxd_password` | Yes |
| `NODE_ENV` | Node.js environment | `development` | Yes |
| `PORT` | Server port | `3000` | Yes |
| `JWT_SECRET` | JWT signing secret | - | Yes |
| `CORS_ORIGIN` | CORS allowed origin | `http://localhost:5173` | No |
| `LOG_LEVEL` | Logging level | `info` | No |
| `SESSION_SECRET` | Session secret key | - | No |

## Docker Environment Variables

The `docker-compose.yml` file includes environment variables for containerized deployment:

### Database Service
```yaml
environment:
  MYSQL_ROOT_PASSWORD: rootpassword
  MYSQL_DATABASE: fxd_erp
  MYSQL_USER: fxd_user
  MYSQL_PASSWORD: fxd_password
```

### Backend Service
```yaml
environment:
  - NODE_ENV=production
  - DB_HOST=database
  - DB_PORT=3306
  - DB_NAME=fxd_erp
  - DB_USER=fxd_user
  - DB_PASSWORD=fxd_password
  - JWT_SECRET=your-jwt-secret-key
  - PORT=3000
```

### Frontend Service
```yaml
environment:
  - VITE_API_URL=http://localhost:3000
  - VITE_ORGANIZATION_ID=default-org-id
  - VITE_API_TIMEOUT=10000
  - VITE_APP_NAME=FxD Partner ERP
  - VITE_APP_VERSION=1.0.0
  - VITE_ENABLE_DEBUG=false
  - VITE_ENABLE_ANALYTICS=false
```

## Environment-Specific Configurations

### Development Environment

For local development, use these values:

**Frontend (.env)**:
```env
VITE_API_URL=http://localhost:3000
VITE_ORGANIZATION_ID=dev-org
VITE_ENABLE_DEBUG=true
```

**Backend (.env)**:
```env
NODE_ENV=development
DB_HOST=localhost
CORS_ORIGIN=http://localhost:5173
LOG_LEVEL=debug
```

### Production Environment

For production deployment, ensure you:

1. **Change all default passwords and secrets**
2. **Use secure JWT secrets** (at least 32 characters)
3. **Set proper CORS origins**
4. **Use environment-specific database credentials**

**Frontend (.env)**:
```env
VITE_API_URL=https://your-api-domain.com
VITE_ORGANIZATION_ID=your-org-id
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=true
```

**Backend (.env)**:
```env
NODE_ENV=production
DB_HOST=your-database-host
DB_PASSWORD=your-secure-database-password
JWT_SECRET=your-very-secure-jwt-secret-at-least-32-characters
CORS_ORIGIN=https://your-frontend-domain.com
LOG_LEVEL=warn
```

### Docker Production Environment

For Docker production deployment, update the `docker-compose.yml` environment variables:

```yaml
# Frontend service environment
environment:
  - VITE_API_URL=https://your-api-domain.com
  - VITE_ORGANIZATION_ID=your-org-id
  - VITE_ENABLE_DEBUG=false
  - VITE_ENABLE_ANALYTICS=true

# Backend service environment  
environment:
  - NODE_ENV=production
  - DB_HOST=database
  - JWT_SECRET=your-very-secure-jwt-secret
  - CORS_ORIGIN=https://your-frontend-domain.com
```

## Security Considerations

1. **Never commit `.env` files** to version control
2. **Use strong, unique secrets** for JWT and sessions
3. **Rotate secrets regularly** in production
4. **Use environment-specific database credentials**
5. **Limit CORS origins** to trusted domains only
6. **Use HTTPS** in production environments

## Troubleshooting

### Common Issues

1. **API calls failing**: Check `VITE_API_URL` matches your backend URL
2. **CORS errors**: Ensure `CORS_ORIGIN` in backend matches frontend URL
3. **Database connection errors**: Verify all `DB_*` variables are correct
4. **Authentication issues**: Check `JWT_SECRET` is set and consistent

### Validation

You can validate your environment setup by:

1. **Frontend**: Check browser console for API configuration
2. **Backend**: Check server logs for database connection status
3. **Docker**: Use `docker-compose logs` to check container startup

## Example Files

### Frontend .env Example
```env
# Copy this to FxDPartnerERP/.env
VITE_API_URL=http://localhost:3000
VITE_ORGANIZATION_ID=default-org-id
VITE_API_TIMEOUT=10000
VITE_APP_NAME=FxD Partner ERP
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=false
```

### Backend .env Example
```env
# Copy this to FxDPartnerERPBackend/.env
NODE_ENV=development
DB_HOST=localhost
DB_PORT=3306
DB_NAME=fxd_erp
DB_USER=fxd_user
DB_PASSWORD=fxd_password
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
PORT=3000
CORS_ORIGIN=http://localhost:5173
