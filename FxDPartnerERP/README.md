# FxD Partner ERP System - Frontend

A comprehensive Enterprise Resource Planning (ERP) solution designed specifically for FxD partners to manage their procurement, sales, inventory, and financial operations efficiently.

🌐 **Live Demo**: [https://bindalkapil.github.io/FxDPartnerERP](https://bindalkapil.github.io/FxDPartnerERP)

## Table of Contents
- [Overview](#overview)
- [Key Features](#key-features)
- [Tech Stack](#tech-stack)
- [System Architecture](#system-architecture)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Development Workflow](#development-workflow)
- [Project Structure](#project-structure)
- [Environment Configuration](#environment-configuration)
- [Available Scripts](#available-scripts)
- [Deployment](#deployment)
- [Contributing](#contributing)

## Overview

The FxD Partner ERP System is a full-stack application consisting of:
- **Frontend**: React-based SPA with TypeScript and Tailwind CSS
- **Backend**: Node.js/Express API with MySQL database
- **Architecture**: Multi-tenant, role-based access control system

## Key Features

### Core Business Modules
- **Multi-tenant Organization Management**: Support for multiple organizations with data isolation
- **User Management**: Role-based access control with granular permissions
- **Partner Management**: Comprehensive customer and supplier management
- **Product & SKU Management**: Hierarchical product catalog with SKU variants
- **Procurement Management**: Vehicle arrivals, purchase records, and supplier management
- **Sales Management**: Order processing, customer management, and delivery tracking
- **Inventory Control**: Real-time inventory tracking with negative inventory management
- **Financial Management**: Payment processing, credit management, and financial reporting

### Advanced Features
- **Role-Based Permissions**: Granular permission system with JSON-based role definitions
- **Real-time Dashboard**: Analytics and KPI monitoring
- **Multi-location Support**: Organization-based data segregation
- **Audit Trails**: Comprehensive logging and tracking
- **Responsive Design**: Mobile-first responsive interface
- **File Management**: Document and attachment handling

## Tech Stack

### Frontend Technologies
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development with full IntelliSense
- **Tailwind CSS** - Utility-first CSS framework for rapid UI development
- **Vite** - Fast build tool and development server
- **React Router DOM** - Client-side routing and navigation

### UI Components & Libraries
- **Radix UI** - Accessible, unstyled UI primitives
  - Dialog, Dropdown Menu, Label, Slot, Toast components
- **Lucide React** - Beautiful, customizable SVG icons
- **React Hot Toast** - Elegant toast notifications
- **Class Variance Authority** - Type-safe component variants
- **Tailwind Merge** - Intelligent Tailwind class merging

### Development Tools
- **ESLint** - Code linting with TypeScript support
- **TypeScript ESLint** - TypeScript-specific linting rules
- **Autoprefixer** - CSS vendor prefixing
- **PostCSS** - CSS processing and optimization

### Backend Integration
- **RESTful API** - Communication with Node.js/Express backend
- **JWT Authentication** - Secure token-based authentication
- **MySQL Database** - Relational database with complex relationships

## System Architecture

```mermaid
graph TB
    subgraph "Frontend (React)"
        A[React App] --> B[Auth Context]
        A --> C[Dashboard Layout]
        A --> D[Business Modules]
        B --> E[JWT Token Management]
        C --> F[Navigation & Sidebar]
        D --> G[Partners Module]
        D --> H[Inventory Module]
        D --> I[Sales Module]
        D --> J[Finance Module]
    end
    
    subgraph "Backend (Node.js)"
        K[Express API] --> L[Auth Middleware]
        K --> M[Business Controllers]
        L --> N[JWT Verification]
        M --> O[Database Models]
    end
    
    subgraph "Database (MySQL)"
        P[Organizations] --> Q[Users]
        P --> R[Products/SKUs]
        P --> S[Customers/Suppliers]
        P --> T[Sales/Purchase Records]
        P --> U[Inventory]
        P --> V[Payments]
    end
    
    A --> K
    O --> P
```

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v16 or higher) - [Download](https://nodejs.org/)
- **npm** (v8 or higher) or **yarn** - Comes with Node.js
- **MySQL** (v8.0 or higher) - [Download](https://dev.mysql.com/downloads/)
- **Git** - [Download](https://git-scm.com/)
- **Modern Web Browser** - Chrome, Firefox, Safari, or Edge

## Installation

### 1. Clone the Repository
```bash
git clone https://github.com/Vegrow-Tech/FxDPartnerERP.git
cd FxDPartnerERP
```

### 2. Backend Setup (Required First)
```bash
# Navigate to backend directory
cd FxDPartnerERPBackend

# Install backend dependencies
npm install

# Set up backend environment
cp .env.example .env
# Edit .env with your database credentials

# Set up MySQL database
mysql -u root -p
CREATE DATABASE fxd_partner_erp;
exit

# Run database migrations
npm run migrate

# Start backend server
npm run dev
```

The backend will run on `http://localhost:3001`

### 3. Frontend Setup
```bash
# Navigate to frontend directory (from project root)
cd FxDPartnerERP

# Install frontend dependencies
npm install

# Set up frontend environment
cp .env.example .env
# Configure API endpoint (see Environment Configuration below)

# Start development server
npm run dev
```

The frontend will run on `http://localhost:5173`

## Development Workflow

### Running Both Services
For full development, you need both backend and frontend running:

```bash
# Terminal 1 - Backend
cd FxDPartnerERPBackend
npm run dev

# Terminal 2 - Frontend
cd FxDPartnerERP
npm run dev
```

### Default Login Credentials
```
Email: <EMAIL>
Password: admin123
```

## Project Structure

```
FxDPartnerERP/
├── public/                     # Static assets
│   ├── 404.html               # 404 error page
│   └── ...
├── src/
│   ├── admin/                 # Admin module
│   │   ├── components/        # Admin-specific components
│   │   ├── contexts/          # Admin contexts
│   │   ├── pages/             # Admin pages
│   │   └── services/          # Admin API services
│   ├── components/            # Reusable UI components
│   │   ├── charts/            # Chart components
│   │   ├── dashboard/         # Dashboard components
│   │   ├── forms/             # Form components
│   │   ├── modals/            # Modal components
│   │   ├── navigation/        # Navigation components
│   │   ├── payments/          # Payment components
│   │   └── ui/                # Base UI components
│   ├── contexts/              # React contexts
│   │   ├── AuthContext.tsx    # Authentication context
│   │   └── SidebarContext.tsx # Sidebar state context
│   ├── hooks/                 # Custom React hooks
│   │   ├── usePaymentFilters.ts
│   │   └── usePayments.ts
│   ├── layouts/               # Layout components
│   │   └── DashboardLayout.tsx
│   ├── lib/                   # Utility libraries
│   │   └── api.ts             # API client configuration
│   ├── pages/                 # Page components
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # Dashboard pages
│   │   ├── finance/           # Financial management pages
│   │   ├── inventory/         # Inventory management pages
│   │   ├── partners/          # Partner management pages
│   │   ├── procurement/       # Procurement pages
│   │   ├── sales/             # Sales management pages
│   │   └── settings/          # Settings pages
│   ├── services/              # API services
│   │   └── api/               # API service modules
│   ├── types/                 # TypeScript type definitions
│   │   └── payment.types.ts
│   ├── utils/                 # Utility functions
│   │   └── orderCalculations.ts
│   ├── App.tsx                # Main application component
│   ├── main.tsx               # Application entry point
│   ├── index.css              # Global styles
│   └── vite-env.d.ts          # Vite type definitions
├── scripts/                   # Build and deployment scripts
├── .env.example               # Environment variables template
├── package.json               # Dependencies and scripts
├── tailwind.config.js         # Tailwind CSS configuration
├── tsconfig.json              # TypeScript configuration
├── vite.config.ts             # Vite configuration
└── README.md                  # This file
```

## Environment Configuration

### Frontend Environment Variables (.env)
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api
VITE_API_TIMEOUT=10000

# Application Configuration
VITE_APP_NAME=FxD Partner ERP
VITE_APP_VERSION=1.0.0

# Development Configuration
VITE_DEV_MODE=true
VITE_ENABLE_LOGGING=true
```

### Required Backend Configuration
Ensure your backend `.env` includes:
```env
PORT=3001
NODE_ENV=development
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=fxd_partner_erp
JWT_SECRET=your_jwt_secret_key
CORS_ORIGIN=http://localhost:5173
```

## Available Scripts

### Development Scripts
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run preview      # Preview production build locally
npm run lint         # Run ESLint for code quality
```

### Deployment Scripts
```bash
npm run predeploy    # Pre-deployment build
npm run deploy       # Deploy to GitHub Pages
```

### Code Quality
```bash
npm run lint         # Check code quality and style
npm run lint:fix     # Auto-fix linting issues
```

## Deployment

### Automatic Deployment (GitHub Pages)
The project is configured for automatic deployment to GitHub Pages:

- **Trigger**: Push to `main` branch
- **URL**: [https://bindalkapil.github.io/FxDPartnerERP](https://bindalkapil.github.io/FxDPartnerERP)
- **Workflow**: `.github/workflows/deploy-pages.yml`

### Manual Deployment
```bash
npm run deploy
```

### Environment Variables for Production
Configure these GitHub repository secrets:
- `VITE_API_BASE_URL`: Production API endpoint
- `VITE_APP_NAME`: Application name
- `VITE_APP_VERSION`: Current version

### Production Build Optimization
- **Code Splitting**: Automatic route-based code splitting
- **Tree Shaking**: Unused code elimination
- **Asset Optimization**: Image and CSS optimization
- **Gzip Compression**: Automatic compression for smaller bundles

## API Integration

### Authentication Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant D as Database
    
    U->>F: Login Request
    F->>B: POST /auth/login
    B->>D: Validate Credentials
    D-->>B: User Data
    B-->>F: JWT Token + User Info
    F->>F: Store Token in Context
    F-->>U: Redirect to Dashboard
    
    Note over F,B: All subsequent requests include JWT token
    F->>B: API Request + JWT
    B->>B: Validate JWT
    B->>D: Query with Organization Filter
    D-->>B: Filtered Data
    B-->>F: Response Data
```

### API Endpoints Overview
- **Authentication**: `/auth/login`, `/auth/refresh`, `/auth/logout`
- **Users**: `/users` - User management with role-based access
- **Organizations**: `/organizations` - Multi-tenant organization management
- **Partners**: `/customers`, `/suppliers` - Partner relationship management
- **Products**: `/products`, `/skus` - Product catalog management
- **Sales**: `/sales-orders` - Sales order processing
- **Procurement**: `/purchase-records`, `/vehicle-arrivals` - Procurement management
- **Inventory**: `/inventory` - Real-time inventory tracking
- **Payments**: `/payments` - Financial transaction management

## Contributing

### Development Guidelines
1. **Code Style**: Follow ESLint configuration
2. **TypeScript**: Maintain strict type safety
3. **Components**: Use functional components with hooks
4. **Styling**: Use Tailwind CSS utility classes
5. **State Management**: Use React Context for global state

### Pull Request Process
1. Create a feature branch from `main`
2. Make your changes with proper TypeScript types
3. Test your changes locally with both frontend and backend
4. Run `npm run lint` to ensure code quality
5. Submit a pull request with detailed description

### Testing
- Test all new features with the backend API
- Verify responsive design on different screen sizes
- Check authentication and authorization flows
- Validate form inputs and error handling

## Troubleshooting

### Common Issues

**Backend Connection Issues**:
- Ensure backend is running on port 3001
- Check CORS configuration in backend
- Verify API base URL in frontend environment

**Authentication Problems**:
- Clear browser localStorage and cookies
- Check JWT token expiration
- Verify backend authentication middleware

**Build Issues**:
- Clear node_modules and reinstall dependencies
- Check TypeScript configuration
- Verify all environment variables are set

### Getting Help
- Check the backend README for API documentation
- Review the database schema in migration files
- Check browser console for detailed error messages

---

Built with ❤️ by the Vegrow Tech Team

**Related Documentation**:
- [Backend API Documentation](../FxDPartnerERPBackend/README.md)
- [Database Schema](../FxDPartnerERPBackend/SCHEMA.md)
- [Deployment Guide](./DEPLOYMENT.md)
