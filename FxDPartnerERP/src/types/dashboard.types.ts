// Simple Dashboard Views
export interface CustomerSupplierView {
  customers: {
    total: number;
    active: number;
    new: number;
    inactive: number;
  };
  suppliers: {
    total: number;
    active: number;
    new: number;
    inactive: number;
  };
}

export interface ArrivalView {
  totalArrivals: number;
  unloaded: number;
  partialClosed: number;
  fullClosed: number;
  poCreationPending: number;
}

export interface InventoryView {
  openingStock: number;
  newArrivals: number;
  totalDispatched: number;
  currentInventory: number;
  totalWeight: number;
  newArrivalWeight: number;
}

export interface SalesPaymentView {
  totalSales: number;
  totalPaymentsCollected: number;
  pendingPayments: number;
  collectionRate: string;
}

export interface PurchasePaymentView {
  totalPurchases: number;
  totalPaymentsMade: number;
  pendingPayments: number;
  paymentRate: string;
}

// Legacy interfaces for backward compatibility
export interface FinancialMetrics {
  revenueTrends: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
  profitability: {
    totalRevenue: number;
    totalCosts: number;
    grossProfit: number;
    grossProfitMargin: number;
  };
  paymentStatus: Array<{
    status: string;
    count: number;
    amount: number;
  }>;
}

export interface OperationalMetrics {
  orderFulfillment: Array<{
    status: string;
    count: number;
  }>;
  inventory: Array<{
    category: string;
    quantity: number;
    products: number;
  }>;
  vehicleArrivals: Array<{
    status: string;
    count: number;
  }>;
  topProducts: Array<{
    name: string;
    category: string;
    revenue: number;
    orders: number;
  }>;
}

export interface CustomerInsights {
  topCustomers: Array<{
    name: string;
    type: string;
    status: string;
    totalRevenue: number;
    totalOrders: number;
    avgOrderValue: number;
  }>;
  customerTypes: Array<{
    type: string;
    count: number;
  }>;
  newCustomers: number;
}

export interface Alert {
  type: string;
  severity: 'high' | 'medium' | 'low';
  title: string;
  message: string;
  timestamp: Date;
}

export interface KPICardProps {
  title: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
  subtitle?: string;
  loading?: boolean;
  color?: 'green' | 'blue' | 'orange' | 'purple' | 'red';
}

export interface TrendChartProps {
  data: Array<{
    date: string;
    value: number;
    label?: string;
  }>;
  title: string;
  color?: string;
  height?: number;
  showGrid?: boolean;
  loading?: boolean;
}

export interface PerformanceGaugeProps {
  value: number;
  max: number;
  title: string;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
}

export interface ComparisonTableProps {
  data: Array<{
    label: string;
    current: number;
    previous: number;
    change: number;
    trend: 'up' | 'down' | 'neutral';
  }>;
  title: string;
  loading?: boolean;
}

export interface AlertPanelProps {
  alerts: Alert[];
  maxAlerts?: number;
  onAlertClick?: (alert: Alert) => void;
  loading?: boolean;
}

export interface DashboardFilters {
  period: '7' | '30' | '90' | '365';
  dateRange?: {
    start: Date;
    end: Date;
  };
  category?: string;
  status?: string;
}
