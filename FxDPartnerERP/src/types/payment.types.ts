export interface Payment {
  id: string;
  type: 'received' | 'made' | 'expense';
  amount: number;
  payment_date: string;
  party_id: string | null;
  party_type: 'customer' | 'supplier' | null;
  party_name: string;
  reference_id: string | null;
  reference_type: 'sales_order' | 'purchase_record' | null;
  reference_number: string | null;
  mode: 'cash' | 'bank_transfer' | 'upi' | 'cheque' | 'online';
  status: 'pending' | 'completed' | 'failed' | 'confirmed';
  notes: string | null;
  proof_attachment_url?: string | null;
  proof_attachment_name?: string | null;
  created_at: string;
  updated_at: string;
  organization?: {
    id: string;
    name: string;
    code: string;
  };
}

export interface PaymentSummary {
  totalReceived: number;
  totalPaid: number;
  totalExpenses: number;
  pendingCount: number;
  netCashFlow: number;
  monthlyGrowth: number;
}

export interface PaymentFilters {
  search: string;
  type: 'all' | 'received' | 'made' | 'expense';
  status: 'all' | 'pending' | 'completed' | 'failed' | 'confirmed';
  mode: 'all' | 'cash' | 'bank_transfer' | 'upi' | 'cheque' | 'online';
  dateRange: {
    from: string;
    to: string;
  };
  partyType: 'all' | 'customer' | 'supplier';
  amountRange: {
    min: number | null;
    max: number | null;
  };
  quickDateFilter?: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
}

export interface PaymentFormData {
  type: 'received' | 'made' | 'expense';
  amount: string;
  payment_date: string;
  party_id: string;
  party_type?: 'customer' | 'supplier';
  party_name: string;
  reference_id?: string;
  reference_type?: 'sales_order' | 'purchase_record';
  reference_number?: string;
  mode: 'cash' | 'bank_transfer' | 'upi' | 'cheque' | 'online';
  status: 'pending' | 'completed' | 'failed' | 'confirmed';
  notes?: string;
}

export interface PaymentAnalytics {
  monthlyTrends: {
    month: string;
    received: number;
    paid: number;
    expenses: number;
  }[];
  paymentMethods: {
    method: string;
    count: number;
    amount: number;
  }[];
  topParties: {
    name: string;
    type: 'customer' | 'supplier';
    totalAmount: number;
    transactionCount: number;
  }[];
  statusDistribution: {
    status: string;
    count: number;
    percentage: number;
  }[];
}

export interface BulkPaymentAction {
  type: 'delete' | 'updateStatus' | 'export' | 'reconcile';
  paymentIds: string[];
  data?: any;
}

export interface PaymentTemplate {
  id: string;
  name: string;
  type: 'received' | 'made' | 'expense';
  party_id: string;
  party_name: string;
  amount: number;
  mode: string;
  notes: string;
  isRecurring: boolean;
  frequency?: 'weekly' | 'monthly' | 'quarterly';
}
