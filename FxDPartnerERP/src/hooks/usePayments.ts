import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { paymentService } from '../services/api/paymentService';
import { Payment, PaymentSummary, PaymentFilters } from '../types/payment.types';

export const usePayments = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);

  const loadPayments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await paymentService.getAll();
      setPayments(data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load payments';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const createPayment = useCallback(async (paymentData: any, proofFile?: File) => {
    try {
      console.log('Creating payment with data:', paymentData);
      const result = await paymentService.create(paymentData, proofFile);
      console.log('Payment creation result:', result);
      toast.success('Payment recorded successfully!');
      await loadPayments(); // Reload payments
      return true;
    } catch (err) {
      console.error('Error in createPayment:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create payment';
      toast.error(errorMessage);
      return false;
    }
  }, [loadPayments]);

  const updatePayment = useCallback(async (id: string, paymentData: any, proofFile?: File) => {
    try {
      await paymentService.update(id, paymentData, proofFile);
      toast.success('Payment updated successfully!');
      await loadPayments(); // Reload payments
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update payment';
      toast.error(errorMessage);
      return false;
    }
  }, [loadPayments]);

  const deletePayment = useCallback(async (id: string) => {
    try {
      await paymentService.delete(id);
      setPayments(prev => prev.filter(payment => payment.id !== id));
      toast.success('Payment deleted successfully!');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete payment';
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const deleteMultiplePayments = useCallback(async (ids: string[]) => {
    try {
      await Promise.all(ids.map(id => paymentService.delete(id)));
      setPayments(prev => prev.filter(payment => !ids.includes(payment.id)));
      setSelectedPayments([]);
      toast.success(`${ids.length} payments deleted successfully!`);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete payments';
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const togglePaymentSelection = useCallback((paymentId: string) => {
    setSelectedPayments(prev => 
      prev.includes(paymentId) 
        ? prev.filter(id => id !== paymentId)
        : [...prev, paymentId]
    );
  }, []);

  const selectAllPayments = useCallback((paymentIds: string[]) => {
    setSelectedPayments(paymentIds);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedPayments([]);
  }, []);

  const getPaymentSummary = useCallback((filteredPayments: Payment[]): PaymentSummary => {
    const completedPayments = filteredPayments.filter(p => p.status === 'completed');
    
    const totalReceived = completedPayments
      .filter(p => p.type === 'received')
      .reduce((sum, p) => sum + Number(p.amount), 0);

    const totalPaid = completedPayments
      .filter(p => p.type === 'made')
      .reduce((sum, p) => sum + Number(p.amount), 0);

    const totalExpenses = completedPayments
      .filter(p => p.type === 'expense')
      .reduce((sum, p) => sum + Number(p.amount), 0);

    const pendingCount = filteredPayments.filter(p => p.status === 'pending').length;
    const netCashFlow = totalReceived - totalPaid - totalExpenses;

    // Calculate monthly growth (simplified)
    const currentMonth = new Date().getMonth();
    const currentMonthPayments = completedPayments.filter(p => 
      new Date(p.payment_date).getMonth() === currentMonth
    );
    const currentMonthTotal = currentMonthPayments.reduce((sum, p) => sum + Number(p.amount), 0);
    
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthPayments = completedPayments.filter(p => 
      new Date(p.payment_date).getMonth() === lastMonth
    );
    const lastMonthTotal = lastMonthPayments.reduce((sum, p) => sum + Number(p.amount), 0);
    
    const monthlyGrowth = lastMonthTotal > 0 
      ? ((currentMonthTotal - lastMonthTotal) / lastMonthTotal) * 100 
      : 0;

    return {
      totalReceived,
      totalPaid,
      totalExpenses,
      pendingCount,
      netCashFlow,
      monthlyGrowth
    };
  }, []);

  useEffect(() => {
    loadPayments();
  }, [loadPayments]);

  return {
    payments,
    loading,
    error,
    selectedPayments,
    loadPayments,
    createPayment,
    updatePayment,
    deletePayment,
    deleteMultiplePayments,
    togglePaymentSelection,
    selectAllPayments,
    clearSelection,
    getPaymentSummary,
    refetch: loadPayments
  };
};
