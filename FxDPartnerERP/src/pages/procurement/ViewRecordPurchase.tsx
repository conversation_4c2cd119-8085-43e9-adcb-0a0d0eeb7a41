import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Package2, <PERSON><PERSON><PERSON><PERSON>, Pencil, Settings } from 'lucide-react';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import { formatQuantityWithUnit, formatWeight } from '../../utils/weightUtils';
import PurchaseRecordClosureModal from '../../components/modals/PurchaseRecordClosureModal';

interface PurchaseRecordData {
  id: string;
  record_number: string;
  supplier: string;
  record_date: string;
  arrival_timestamp: string;
  pricing_model: string;
  default_commission: number | null;
  payment_terms: number | null;
  items_subtotal: number;
  additional_costs_total: number;
  total_amount: number;
  status: string;
  notes: string | null;
  purchase_record_items?: Array<{
    id: string;
    product_name: string;
    sku_code: string;
    category: string;
    quantity: number;
    unit_type: string;
    total_weight: number;
    market_price: number | null;
    commission: number | null;
    unit_price: number;
    total: number;
  }>;
  items?: Array<{
    id: string;
    product_name: string;
    sku_code: string;
    category: string;
    quantity: number;
    unit_type: string;
    total_weight: number;
    market_price: number | null;
    commission: number | null;
    unit_price: number;
    total: number;
  }>;
  purchase_record_costs?: Array<{
    id: string;
    name: string;
    amount: number;
    type: string;
    calculated_amount: number;
  }>;
  costs?: Array<{
    id: string;
    name: string;
    amount: number;
    type: string;
    calculated_amount: number;
  }>;
}

const ViewRecordPurchase: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [orderData, setOrderData] = useState<PurchaseRecordData | null>(null);
  const [loading, setLoading] = useState(true);
  const [closureModal, setClosureModal] = useState<{
    isOpen: boolean;
    recordId: string;
    currentStatus: string;
    recordNumber: string;
  }>({
    isOpen: false,
    recordId: '',
    currentStatus: '',
    recordNumber: ''
  });

  useEffect(() => {
    if (id) {
      loadOrderData();
    }
  }, [id]);

  const loadOrderData = async () => {
    if (!id) return;
    
    try {
      const data = await purchaseRecordService.getById(id);
      setOrderData(data);
    } catch (error) {
      console.error('Error loading vehicle arrival:', error);
      // Global error handler will show the backend error message
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'partial_closure':
        return 'Partial Closure';
      case 'full_closure':
        return 'Full Closure';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'partial_closure':
        return 'bg-yellow-100 text-yellow-800';
      case 'full_closure':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleOpenClosureModal = () => {
    if (orderData) {
      setClosureModal({
        isOpen: true,
        recordId: orderData.id,
        currentStatus: orderData.status,
        recordNumber: orderData.record_number
      });
    }
  };

  const handleCloseClosureModal = () => {
    setClosureModal({
      isOpen: false,
      recordId: '',
      currentStatus: '',
      recordNumber: ''
    });
  };

  const handleStatusUpdated = () => {
    loadOrderData();
  };

  const getCostTypeDisplay = (type: string) => {
    switch (type) {
      case 'fixed':
        return 'Fixed (₹)';
      case 'percentage':
        return 'Percentage (%)';
      case 'per_box':
        return 'Per Box (₹/box)';
      default:
        return type;
    }
  };

  // Helper functions to safely access items and costs
  const getItems = (data: PurchaseRecordData) => {
    return data.purchase_record_items || data.items || [];
  };

  const getCosts = (data: PurchaseRecordData) => {
    return data.purchase_record_costs || data.costs || [];
  };

  // Helper function to safely format numbers
  const safeToFixed = (value: number | string | null | undefined, decimals: number = 2): string => {
    const num = Number(value) || 0;
    return num.toFixed(decimals);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading purchase record...</div>
      </div>
    );
  }

  if (!orderData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Purchase record not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-3 sm:space-x-4">
          <button
            onClick={() => navigate('/record-purchase')}
            className="text-gray-600 hover:text-gray-900 p-1"
          >
            <ArrowLeft className="h-5 w-5 sm:h-6 sm:w-6" />
          </button>
          <div className="flex items-center">
            <Package2 className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
            <h1 className="text-lg sm:text-2xl font-bold text-gray-800">View Purchase Record</h1>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          {orderData.status === 'partial_closure' && (
            <button
              onClick={() => navigate(`/record-purchase/edit/${id}`)}
              className="bg-green-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
            >
              <Pencil className="h-4 w-4 mr-1" />
              <span className="hidden xs:inline">Edit Record</span>
              <span className="xs:hidden">Edit</span>
            </button>
          )}
          {orderData.status !== 'cancelled' && orderData.status !== 'full_closure' && (
            <button
              onClick={handleOpenClosureModal}
              className="bg-blue-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center"
            >
              <Settings className="h-4 w-4 mr-1" />
              <span className="hidden xs:inline">Manage Closure</span>
              <span className="xs:hidden">Manage</span>
            </button>
          )}
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Basic Details */}
          <div>
            <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Record Details</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Record Number</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900 break-all">{orderData.record_number}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Supplier</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{orderData.supplier}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Record Date</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{formatDateTime(orderData.record_date)}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Arrival Timestamp</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{formatDateTime(orderData.arrival_timestamp)}</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Payment Terms</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">{orderData.payment_terms} days</p>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Pricing Model</label>
                <p className="mt-1 text-sm sm:text-base text-gray-900">
                  {orderData.pricing_model === 'commission' ? 'Commission Sale' : 'Fixed Price'}
                </p>
              </div>
              {orderData.pricing_model === 'commission' && orderData.default_commission && (
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-500">Default Commission</label>
                  <p className="mt-1 text-sm sm:text-base text-gray-900">{orderData.default_commission}%</p>
                </div>
              )}
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-500">Status</label>
                <span className={`mt-1 inline-flex px-2 py-1 text-xs leading-4 font-semibold rounded-full ${getStatusColor(orderData.status)}`}>
                  {getStatusDisplay(orderData.status)}
                </span>
              </div>
            </div>
          </div>

          {/* Items */}
          <div className="border-t pt-4 sm:pt-6">
            <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Items</h2>
            
            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product Details
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity & Weight
                    </th>
                    {orderData.pricing_model === 'commission' ? (
                      <>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Market Price (₹)
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Commission (%)
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price (₹)
                        </th>
                      </>
                    ) : (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price (₹)
                      </th>
                    )}
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total (₹)
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {getItems(orderData).map((item, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 table-col-lg">
                        <div>
                          <div className="text-sm font-medium text-gray-900 break-words">{item.product_name}</div>
                          <div className="text-sm text-gray-500 break-words">SKU: {item.sku_code}</div>
                          <div className="text-sm text-gray-500 break-words">Category: {item.category}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 table-col-md">
                        <div>
                          <div className="text-sm text-gray-900">{formatQuantityWithUnit(item.quantity, item.unit_type)}</div>
                          <div className="text-sm text-gray-500">Total Weight: {formatWeight(item.total_weight)}</div>
                        </div>
                      </td>
                      {orderData.pricing_model === 'commission' ? (
                        <>
                          <td className="px-6 py-4 table-col-sm text-sm text-gray-900">
                            ₹{safeToFixed(item.market_price || 0)}
                          </td>
                          <td className="px-6 py-4 table-col-sm text-sm text-gray-900">
                            {safeToFixed(item.commission || 0, 1)}%
                          </td>
                          <td className="px-6 py-4 table-col-sm text-sm text-gray-900">
                            ₹{safeToFixed(item.unit_price)}
                          </td>
                        </>
                      ) : (
                        <td className="px-6 py-4 table-col-sm text-sm text-gray-900">
                          ₹{safeToFixed(item.unit_price)}
                        </td>
                      )}
                      <td className="px-6 py-4 table-col-sm text-sm font-medium text-gray-900">
                        ₹{safeToFixed(item.total)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td colSpan={orderData.pricing_model === 'commission' ? 5 : 3} className="px-6 py-3 text-sm font-medium text-gray-900 text-right">
                      Items Subtotal:
                    </td>
                    <td className="px-6 py-3 text-sm font-medium text-gray-900">
                      ₹{safeToFixed(orderData.items_subtotal)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* Mobile/Tablet Cards */}
            <div className="lg:hidden space-y-4">
              {getItems(orderData).map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4 border">
                  <div className="space-y-3">
                    {/* Product Info */}
                    <div>
                      <h3 className="text-sm sm:text-base font-medium text-gray-900">{item.product_name}</h3>
                      <div className="text-xs sm:text-sm text-gray-500 space-y-1 mt-1">
                        <div>SKU: {item.sku_code}</div>
                        <div>Category: {item.category}</div>
                      </div>
                    </div>

                    {/* Quantity & Weight */}
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="text-xs text-gray-500">Quantity</div>
                        <div className="text-sm font-medium text-gray-900">
                          {formatQuantityWithUnit(item.quantity, item.unit_type)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500">Total Weight</div>
                        <div className="text-sm font-medium text-gray-900">{formatWeight(item.total_weight)}</div>
                      </div>
                    </div>

                    {/* Pricing Details */}
                    <div className="grid grid-cols-2 gap-4">
                      {orderData.pricing_model === 'commission' ? (
                        <>
                          <div>
                            <div className="text-xs text-gray-500">Market Price</div>
                            <div className="text-sm font-medium text-gray-900">₹{safeToFixed(item.market_price || 0)}</div>
                          </div>
                          <div>
                            <div className="text-xs text-gray-500">Commission</div>
                            <div className="text-sm font-medium text-gray-900">{safeToFixed(item.commission || 0, 1)}%</div>
                          </div>
                          <div>
                            <div className="text-xs text-gray-500">Unit Price</div>
                            <div className="text-sm font-medium text-gray-900">₹{safeToFixed(item.unit_price)}</div>
                          </div>
                        </>
                      ) : (
                        <div>
                          <div className="text-xs text-gray-500">Unit Price</div>
                          <div className="text-sm font-medium text-gray-900">₹{safeToFixed(item.unit_price)}</div>
                        </div>
                      )}
                      <div className={orderData.pricing_model === 'commission' ? '' : 'text-right'}>
                        <div className="text-xs text-gray-500">Total</div>
                        <div className="text-sm font-bold text-gray-900">₹{safeToFixed(item.total)}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Items Subtotal */}
              <div className="bg-gray-100 rounded-lg p-4 border-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Items Subtotal:</span>
                  <span className="text-base font-bold text-gray-900">₹{safeToFixed(orderData.items_subtotal)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Costs */}
          {getCosts(orderData).length > 0 && (
            <div className="border-t pt-4 sm:pt-6">
              <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Additional Costs</h2>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                <div className="space-y-3">
                  {getCosts(orderData).map((cost, index) => (
                    <div key={index} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 bg-white rounded-md border space-y-2 sm:space-y-0">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0">
                        <span className="text-sm font-medium text-gray-700 sm:min-w-[120px]">{cost.name}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs sm:text-sm text-gray-600">
                            {cost.amount} {getCostTypeDisplay(cost.type).split(' ')[1]}
                          </span>
                          <span className="text-xs text-gray-500">
                            ({getCostTypeDisplay(cost.type)})
                          </span>
                        </div>
                      </div>
                      <span className="text-sm font-medium text-gray-900 self-end sm:self-auto">
                        ₹{safeToFixed(cost.calculated_amount)}
                      </span>
                    </div>
                  ))}
                  
                  <div className="border-t pt-3 mt-3">
                    <div className="flex justify-between text-sm font-medium">
                      <span className="text-gray-700">Total Additional Costs:</span>
                      <span className="text-gray-900">₹{safeToFixed(orderData.additional_costs_total)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {orderData.notes && (
            <div className="border-t pt-4 sm:pt-6">
              <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Additional Notes</h2>
              <div className="bg-gray-50 rounded-md p-3 sm:p-4">
                <p className="text-sm text-gray-600 whitespace-pre-wrap break-words">{orderData.notes}</p>
              </div>
            </div>
          )}

          {/* Total Summary */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="bg-gray-50 rounded-lg p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Purchase Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Items Subtotal:</span>
                  <span className="text-gray-900 font-medium">₹{safeToFixed(orderData.items_subtotal)}</span>
                </div>
                
                {getCosts(orderData).length > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Less: Additional Costs:</span>
                    <span className="text-red-600 font-medium">-₹{safeToFixed(orderData.additional_costs_total)}</span>
                  </div>
                )}
                
                <div className="border-t pt-3 flex justify-between text-base sm:text-lg font-bold">
                  <span className="text-gray-900">Final Total Amount:</span>
                  <span className="text-green-600">₹{safeToFixed(orderData.total_amount)}</span>
                </div>
                
                {orderData.pricing_model === 'commission' && (
                  <div className="mt-4 pt-3 border-t">
                    <div className="text-xs text-gray-500">
                      * Commission-based pricing: Market price minus commission percentage
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Closure Modal */}
      <PurchaseRecordClosureModal
        isOpen={closureModal.isOpen}
        onClose={handleCloseClosureModal}
        recordId={closureModal.recordId}
        currentStatus={closureModal.currentStatus}
        recordNumber={closureModal.recordNumber}
        onStatusUpdated={handleStatusUpdated}
      />
    </div>
  );
};

export default ViewRecordPurchase;
