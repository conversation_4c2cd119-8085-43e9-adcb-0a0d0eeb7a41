import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Search } from 'lucide-react';

interface DropdownOption {
  value: string;
  label: string;
}

interface EditableDropdownProps {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowCustomValue?: boolean;
  loading?: boolean;
  onSearch?: (query: string) => void;
}

const EditableDropdown: React.FC<EditableDropdownProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select or type...',
  className = '',
  disabled = false,
  allowCustomValue = true,
  loading = false,
  onSearch
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [inputValue, setInputValue] = useState(value || '');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  // Update input value when value prop changes
  useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  // Filter options based on search query
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    option.value.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Check if current value matches any option
  const isCustomValue = value && !options.find(opt => opt.value === value);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchQuery('');
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchQuery(newValue);
    setInputValue(newValue);
    setSelectedIndex(-1);
    
    if (onSearch) {
      onSearch(newValue);
    }
    
    // If allowing custom values, update the parent immediately
    if (allowCustomValue) {
      onChange(newValue);
    }
  };

  const handleBlur = () => {
    // When the input loses focus, ensure the value is saved
    if (allowCustomValue && searchQuery && searchQuery !== value) {
      onChange(searchQuery);
    }
    // Don't close dropdown immediately to allow clicking on options
    setTimeout(() => {
      setIsOpen(false);
      setSearchQuery('');
      setSelectedIndex(-1);
    }, 200);
  };

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setInputValue(optionValue);
    setIsOpen(false);
    setSearchQuery('');
    setSelectedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen && (e.key === 'ArrowDown' || e.key === 'Enter')) {
      e.preventDefault();
      setIsOpen(true);
      return;
    }

    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredOptions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && filteredOptions[selectedIndex]) {
          handleSelect(filteredOptions[selectedIndex].value);
        } else if (allowCustomValue && searchQuery) {
          handleSelect(searchQuery);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSearchQuery('');
        setSelectedIndex(-1);
        break;
    }
  };

  const getDropdownPosition = () => {
    if (!inputRef.current) return {};

    const rect = inputRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = Math.min(filteredOptions.length * 40 + 100, 300);
    
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    
    if (spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove) {
      return {
        top: '100%',
        bottom: 'auto',
        marginTop: '4px'
      };
    } else {
      return {
        top: 'auto',
        bottom: '100%',
        marginBottom: '4px'
      };
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={isOpen ? searchQuery : inputValue}
          onChange={handleInputChange}
          onFocus={() => {
            setIsOpen(true);
            setSearchQuery(inputValue);
          }}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          placeholder={placeholder}
          className={`
            block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm
            focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500
            ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : ''}
            ${isCustomValue ? 'text-gray-500' : ''}
            sm:text-sm
          `}
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <ChevronDown 
            className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`} 
          />
        </div>
      </div>

      {isOpen && (
        <div
          className="absolute z-[9999] w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
          style={getDropdownPosition()}
        >
          {loading ? (
            <div className="px-3 py-2 text-sm text-gray-500 text-center">
              Loading...
            </div>
          ) : (
            <>
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option, index) => (
                  <div
                    key={option.value}
                    onClick={() => handleSelect(option.value)}
                    className={`
                      cursor-pointer select-none relative py-2 pl-10 pr-9 
                      ${index === selectedIndex ? 'bg-green-50 text-green-900' : 'text-gray-900 hover:bg-gray-50'}
                      ${inputValue === option.value ? 'font-medium' : 'font-normal'}
                    `}
                  >
                    <span className="block truncate">{option.label}</span>
                    {inputValue === option.value && (
                      <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-green-600">
                        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </span>
                    )}
                  </div>
                ))
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  {searchQuery && allowCustomValue ? (
                    <div
                      onClick={() => handleSelect(searchQuery)}
                      className="cursor-pointer hover:bg-gray-50 -mx-3 px-3 py-1"
                    >
                      <span className="font-medium">{searchQuery}</span>
                    </div>
                  ) : (
                    'No options found'
                  )}
                </div>
              )}
              {searchQuery && allowCustomValue && filteredOptions.length > 0 && 
               !filteredOptions.some(opt => opt.value.toLowerCase() === searchQuery.toLowerCase() || opt.label.toLowerCase() === searchQuery.toLowerCase()) && (
                <div className="border-t border-gray-200 mt-1 pt-1">
                  <div
                    onClick={() => handleSelect(searchQuery)}
                    className="cursor-pointer select-none relative py-2 pl-10 pr-9 text-gray-900 hover:bg-gray-50"
                  >
                    <span className="font-medium">{searchQuery}</span>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default EditableDropdown;
