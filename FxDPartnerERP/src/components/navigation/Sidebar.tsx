import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useSidebar } from '../../contexts/SidebarContext';
import { usePermissions } from '../../hooks/usePermissions';
import { Truck, ClipboardList, Package, ShoppingCart, Truck as TruckLoading, Users, User, BookOpen, CreditCard, Home, Clock } from 'lucide-react';

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  closeSidebar?: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, closeSidebar }) => {
  const { isSidebarOpen } = useSidebar();
  
  const handleClick = () => {
    if (window.innerWidth < 768 && closeSidebar) {
      closeSidebar();
    }
  };

  return (
    <NavLink 
      to={to} 
      onClick={handleClick}
      className={({ isActive }) => `
        flex items-center px-4 py-3 text-gray-700 transition-colors duration-200
        ${isActive 
          ? 'bg-green-50 text-green-600 border-r-4 border-green-600' 
          : 'hover:bg-gray-100'
        }
        ${!isSidebarOpen && 'justify-center'}
      `}
    >
      <span className={!isSidebarOpen ? 'mr-0' : 'mr-3'}>{icon}</span>
      {isSidebarOpen && <span className="font-medium max-w-[200px]">{label}</span>}
    </NavLink>
  );
};

const Sidebar: React.FC = () => {
  const { user } = useAuth();
  const { isSidebarOpen, toggleSidebar, closeSidebar } = useSidebar();
  const {
    canReadPurchase,
    canReadInventory,
    canReadSales,
    canReadPayment,
    canReadCustomer,
    canReadSupplier,
    canViewReports,
    canApproveSales,
    canAccessPage
  } = usePermissions();

  if (!user) return null;

  return (
    <>
      {/* Mobile Overlay */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden" 
          onClick={closeSidebar}
        />
      )}

      {/* Sidebar */}
      <aside 
        className={`
          fixed top-0 left-0 z-30 h-full bg-white border-r shadow-sm
          transform transition-all duration-300 ease-in-out
          ${isSidebarOpen 
            ? 'w-64 translate-x-0' 
            : 'w-64 -translate-x-full md:w-20 md:translate-x-0'
          }
        `}
      >
        {/* Sidebar Header */}
        <div 
          className="flex items-center h-16 px-4 border-b cursor-pointer"
          onClick={toggleSidebar}
        >
          <div className="flex items-center">
            <span className="text-green-600">
              <Package size={24} />
            </span>
            {isSidebarOpen && (
              <h1 className="text-xl font-bold text-gray-800 ml-2">FxD Partner ERP</h1>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="mt-2 overflow-y-auto h-[calc(100vh-4rem)]">
          {/* Dashboard - Always show */}
          <NavItem to="/" icon={<Home size={20} />} label="Dashboard" closeSidebar={closeSidebar} />
          
          {/* Procurement Section */}
          {(canReadPurchase() || canAccessPage('vehicle_arrival') || canAccessPage('record_purchase')) && (
            <>
              {isSidebarOpen && <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase">Procurement</div>}
              {(canReadPurchase() || canAccessPage('vehicle_arrival')) && (
                <NavItem to="/vehicle-arrival" icon={<Truck size={20} />} label="Vehicle Arrival" closeSidebar={closeSidebar} />
              )}
              {(canReadPurchase() || canAccessPage('record_purchase')) && (
                <NavItem to="/record-purchase" icon={<ClipboardList size={20} />} label="Record Purchase" closeSidebar={closeSidebar} />
              )}
            </>
          )}
          
          {/* Inventory Section */}
          {(canReadInventory() || canAccessPage('inventory')) && (
            <>
              {isSidebarOpen && <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase">Inventory</div>}
              <NavItem to="/inventory" icon={<Package size={20} />} label="Inventory" closeSidebar={closeSidebar} />
            </>
          )}
          
          {/* Sales Section */}
          {(canReadSales() || canAccessPage('sales') || canAccessPage('dispatch')) && (
            <>
              {isSidebarOpen && <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase">Sales</div>}
              {(canReadSales() || canAccessPage('sales')) && (
                <NavItem to="/sales" icon={<ShoppingCart size={20} />} label="Sales" closeSidebar={closeSidebar} />
              )}
              {(canReadSales() || canAccessPage('dispatch')) && (
                <NavItem to="/dispatch" icon={<TruckLoading size={20} />} label="Dispatch" closeSidebar={closeSidebar} />
              )}
            </>
          )}
          
          {/* Financials Section */}
          {(canViewReports() || canReadPayment() || canAccessPage('ledger') || canAccessPage('payments')) && (
            <>
              {isSidebarOpen && <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase">Financials</div>}
              {(canViewReports() || canAccessPage('ledger')) && (
                <NavItem to="/ledger" icon={<BookOpen size={20} />} label="Transactions" closeSidebar={closeSidebar} />
              )}
              {(canReadPayment() || canAccessPage('payments')) && (
                <NavItem to="/payments" icon={<CreditCard size={20} />} label="Payments" closeSidebar={closeSidebar} />
              )}
            </>
          )}
          
          {/* Partners Section */}
          {(canReadSupplier() || canReadCustomer() || canAccessPage('suppliers') || canAccessPage('customers')) && (
            <>
              {isSidebarOpen && <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase">Partners</div>}
              {(canReadSupplier() || canAccessPage('suppliers')) && (
                <NavItem to="/suppliers" icon={<Users size={20} />} label="Suppliers" closeSidebar={closeSidebar} />
              )}
              {(canReadCustomer() || canAccessPage('customers')) && (
                <NavItem to="/customers" icon={<User size={20} />} label="Customers" closeSidebar={closeSidebar} />
              )}
            </>
          )}
        </nav>
      </aside>
    </>
  );
};

export default Sidebar;
