import { apiRequest } from './config';

export const salesService = {
  // Get all sales orders
  getAll: async (filters?: { startDate?: string; endDate?: string }, showGlobalError: boolean = true) => {
    const queryParams = new URLSearchParams();
    if (filters?.startDate) queryParams.append('startDate', filters.startDate);
    if (filters?.endDate) queryParams.append('endDate', filters.endDate);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/sales?${queryString}` : '/api/sales';
    
    return await apiRequest(url, {}, showGlobalError);
  },

  // Get single sales order
  getById: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/${id}`, {}, showGlobalError);
  },

  // Get sales orders by customer
  getByCustomer: async (customerId: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/customer/${customerId}`, {}, showGlobalError);
  },

  // Create sales order
  create: async (data: any, showGlobalError: boolean = true) => {
    return await apiRequest('/api/sales', {
      method: 'POST',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Create sales order with multiple payments
  createWithPayments: async (orderData: any, paymentMethods: any[], showGlobalError: boolean = true) => {
    return await apiRequest('/api/sales/with-payments', {
      method: 'POST',
      body: JSON.stringify({ orderData, paymentMethods }),
    }, showGlobalError);
  },

  // Update sales order
  update: async (id: string, data: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Cancel sales order
  cancel: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/${id}/cancel`, {
      method: 'PATCH',
    }, showGlobalError);
  },

  // Update dispatch details
  updateDispatch: async (id: string, data: any, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/${id}/dispatch`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    }, showGlobalError);
  },

  // Get outstation sales orders
  getOutstation: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/sales/outstation', {}, showGlobalError);
  },

  // Approve pending sales order
  approve: async (id: string, newStatus?: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/${id}/approve`, {
      method: 'PATCH',
      body: JSON.stringify({ newStatus }),
    }, showGlobalError);
  },

  // Reject pending sales order
  reject: async (id: string, rejectionReason: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/${id}/reject`, {
      method: 'PATCH',
      body: JSON.stringify({ rejectionReason }),
    }, showGlobalError);
  },

  // Check inventory for sales order
  checkInventory: async (items: any[], showGlobalError: boolean = true) => {
    return await apiRequest('/api/sales/check-inventory', {
      method: 'POST',
      body: JSON.stringify({ items }),
    }, showGlobalError);
  },

  // Delete sales order
  delete: async (id: string, showGlobalError: boolean = true) => {
    return await apiRequest(`/api/sales/${id}`, {
      method: 'DELETE',
    }, showGlobalError);
  },

  // Get sales analytics
  getAnalytics: async (showGlobalError: boolean = true) => {
    return await apiRequest('/api/sales/analytics', {}, showGlobalError);
  },
};
