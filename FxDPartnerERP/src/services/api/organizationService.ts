import { apiRequest } from './config';

export interface Organization {
  id: string;
  name: string;
  code: string;
  email?: string;
  phone?: string;
  address?: string;
  gst_number?: string;
  pan_number?: string;
  owner_name?: string;
  default_commission_percentage: number;
  status: 'active' | 'inactive';
  subscription_plan: string;
  subscription_expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UpdateOrganizationData {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  owner_name?: string;
  default_commission_percentage?: number;
}

class OrganizationService {
  private baseUrl = '/api/organizations';

  async getCurrentOrganization(): Promise<Organization> {
    return apiRequest(`${this.baseUrl}/current`, {
      method: 'GET'
    });
  }

  async updateCurrentOrganization(data: UpdateOrganizationData): Promise<Organization> {
    return apiRequest(`${this.baseUrl}/current`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }
}

export const organizationService = new OrganizationService();
