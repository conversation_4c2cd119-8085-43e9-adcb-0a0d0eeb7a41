const mysql = require('mysql2/promise');

async function checkInventoryTransactionsSchema() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'fxd_partner_erp'
  });

  try {
    console.log('🔍 Checking inventory_transactions table schema...');
    
    // Check if unit_type column exists
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM inventory_transactions LIKE 'unit_type'
    `);
    
    if (columns.length > 0) {
      console.log('✅ unit_type column EXISTS in inventory_transactions table:');
      console.log(columns[0]);
    } else {
      console.log('❌ unit_type column does NOT exist in inventory_transactions table');
    }
    
    // Show all columns in inventory_transactions table
    console.log('\n📋 All columns in inventory_transactions table:');
    const [allColumns] = await connection.execute(`
      SHOW COLUMNS FROM inventory_transactions
    `);
    
    allColumns.forEach((col, index) => {
      console.log(`${index + 1}. ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
    });
    
    // Check recent transactions to see what unit_type values exist
    console.log('\n📊 Sample inventory transactions with unit_type values:');
    const [sampleTransactions] = await connection.execute(`
      SELECT id, transaction_type, unit_type, created_at 
      FROM inventory_transactions 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    if (sampleTransactions.length > 0) {
      sampleTransactions.forEach(txn => {
        console.log(`ID: ${txn.id}, Type: ${txn.transaction_type}, Unit Type: ${txn.unit_type}, Created: ${txn.created_at}`);
      });
    } else {
      console.log('No transactions found');
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  } finally {
    await connection.end();
  }
}

checkInventoryTransactionsSchema();
