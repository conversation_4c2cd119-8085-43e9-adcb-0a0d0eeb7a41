# How to Run FxD Partner ERP on Your Local Docker

This is a step-by-step guide to get your ERP application running on your local machine using Docker.

## Prerequisites Check

First, make sure you have Docker installed and running:

```bash
# Check if Docker is installed
docker --version

# Check if Docker Compose is installed
docker-compose --version

# If you get "command not found" errors, Docker is not installed or not in PATH
```

### Install Docker Desktop (if not installed)

If you get "command not found" errors, you need to install Docker Desktop:

1. **Download Docker Desktop for Mac:**
   - Go to: https://www.docker.com/products/docker-desktop
   - Download "Docker Desktop for Mac"
   - Choose the right version for your Mac (Intel or Apple Silicon)

2. **Install Docker Desktop:**
   - Open the downloaded `.dmg` file
   - Drag Docker to Applications folder
   - Launch Docker from Applications

3. **Complete Setup:**
   - Docker will ask for system permissions
   - Accept the license agreement
   - Wait for Docker to start completely

4. **Verify Installation:**
   ```bash
   # These should now work without errors
   docker --version
   docker-compose --version
   ```

### Start Docker Desktop (IMPORTANT!)

The error you encountered means Docker Desktop is not running. You need to start it:

**Method 1: Using Applications**
1. Open **Applications** folder
2. Find and double-click **Docker Desktop**
3. Wait for Docker to start (you'll see the Docker whale icon in your menu bar)

**Method 2: Using Spotlight**
1. Press `Cmd + Space`
2. Type "Docker Desktop"
3. Press Enter to launch

**Method 3: Using Terminal**
```bash
# Open Docker Desktop from terminal
open -a Docker

# Wait for Docker to start, then verify it's running
docker info
```

**Verify Docker is Running:**
```bash
# This should show Docker system info without errors
docker info

# This should show running containers (probably empty initially)
docker ps
```

If you still get "Cannot connect to the Docker daemon" error, restart Docker Desktop:
1. Click the Docker whale icon in your menu bar
2. Select "Restart Docker Desktop"
3. Wait for it to fully restart

## Step-by-Step Setup

### Step 1: Navigate to Your Project Directory

```bash
cd /Users/<USER>/Documents/vegrow/FXD_ERP
```

### Step 2: Create Environment File for Backend

```bash
# Copy the example environment file
cp FxDPartnerERPBackend/.env.example FxDPartnerERPBackend/.env

# Edit the .env file with these settings:
```

Create `FxDPartnerERPBackend/.env` with this content:
```env
NODE_ENV=production
DB_HOST=database
DB_PORT=3306
DB_NAME=fxd_erp
DB_USER=fxd_user
DB_PASSWORD=fxd_password
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
PORT=3000
```

### Step 3: Build and Start All Services

```bash
# Build and start all services in detached mode
docker-compose up -d --build
```

This command will:
- Build the frontend Docker image
- Build the backend Docker image
- Pull the MySQL image
- Start all three containers
- Run database migrations automatically

### Step 4: Monitor the Startup Process

```bash
# Watch the logs to see everything starting up
docker-compose logs -f

# Or watch specific services:
docker-compose logs -f database    # Database logs
docker-compose logs -f backend     # Backend logs (includes migration output)
docker-compose logs -f frontend    # Frontend logs
```

### Step 5: Verify Everything is Running

```bash
# Check container status
docker-compose ps

# You should see something like:
#        Name                      Command               State           Ports         
# -------------------------------------------------------------------------------------
# fxd-erp-database     docker-entrypoint.sh mysqld      Up      0.0.0.0:3306->3306/tcp
# fxd-erp-backend      /app/start.sh                    Up      0.0.0.0:3000->3000/tcp
# fxd-erp-frontend     nginx -g daemon off;             Up      0.0.0.0:80->80/tcp
```

### Step 6: Access Your Application

Open your web browser and go to:

- **Frontend Application**: http://localhost
- **Backend API**: http://localhost:3000
- **Database**: Connect using any MySQL client to `localhost:3306`

## Common Commands for Local Development

### Starting and Stopping

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart a specific service
docker-compose restart backend

# Rebuild and restart (after code changes)
docker-compose up -d --build
```

### Viewing Logs

```bash
# All logs
docker-compose logs -f

# Specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f database

# Last 100 lines of logs
docker-compose logs --tail=100 backend
```

### Accessing Containers

```bash
# Access backend container shell
docker-compose exec backend bash

# Access database container
docker-compose exec database mysql -u root -p

# Access frontend container (Alpine Linux)
docker-compose exec frontend sh
```

### Database Operations

```bash
# Run migrations manually
docker-compose exec backend npm run migrate

# Access MySQL directly
docker-compose exec database mysql -u fxd_user -p fxd_erp

# Backup database
docker-compose exec database mysqldump -u root -p fxd_erp > backup.sql

# View database logs
docker-compose logs database
```

## Troubleshooting Local Issues

### Port Already in Use

If you get port conflicts:

```bash
# Check what's using the ports
lsof -i :80    # Frontend port
lsof -i :3000  # Backend port  
lsof -i :3306  # Database port

# Kill processes using these ports or change ports in docker-compose.yml
```

### Container Won't Start

```bash
# Check detailed logs
docker-compose logs backend

# Rebuild without cache
docker-compose build --no-cache backend

# Remove all containers and start fresh
docker-compose down -v
docker-compose up -d --build
```

### Database Connection Issues

```bash
# Check if database is ready
docker-compose exec database mysql -u root -p -e "SHOW DATABASES;"

# Check backend can connect to database
docker-compose exec backend npm run migrate
```

### Frontend Not Loading

```bash
# Check if frontend container is running
docker-compose ps frontend

# Check frontend logs
docker-compose logs frontend

# Rebuild frontend
docker-compose build --no-cache frontend
docker-compose up -d frontend
```

## Development Workflow

### Making Code Changes

1. **Frontend Changes**: 
   ```bash
   # After making changes to React code
   docker-compose build frontend
   docker-compose up -d frontend
   ```

2. **Backend Changes**:
   ```bash
   # After making changes to Node.js code
   docker-compose build backend
   docker-compose up -d backend
   ```

3. **Database Changes**:
   ```bash
   # After adding new migration files
   docker-compose exec backend npm run migrate
   ```

### Complete Rebuild

```bash
# When you want to start completely fresh
docker-compose down -v  # WARNING: This deletes database data
docker-compose build --no-cache
docker-compose up -d
```

## File Structure Reminder

Your project should look like this:
```
FXD_ERP/
├── FxDPartnerERP/          # Frontend code
│   ├── Dockerfile
│   ├── .dockerignore
│   └── package.json
├── FxDPartnerERPBackend/   # Backend code
│   ├── Dockerfile
│   ├── .dockerignore
│   ├── .env               # Create this file
│   └── package.json
├── docker-compose.yml
└── LOCAL_DOCKER_SETUP.md  # This file
```

## Quick Reference Commands

```bash
# Start everything
docker-compose up -d

# View all logs
docker-compose logs -f

# Stop everything
docker-compose down

# Rebuild and restart
docker-compose up -d --build

# Check status
docker-compose ps

# Access backend shell
docker-compose exec backend bash

# Run migrations
docker-compose exec backend npm run migrate
```

## Success Indicators

You'll know everything is working when:

1. `docker-compose ps` shows all containers as "Up"
2. http://localhost loads your frontend application
3. http://localhost:3000 shows your backend API
4. Backend logs show "Running database migrations..." followed by "Starting the application..."
5. No error messages in the logs

If you encounter any issues, check the logs first: `docker-compose logs -f`
