# FxD Partner ERP Backend - Cline Rules

## Project Overview
This is a Node.js + Express + TypeScript backend API for an ERP system with multi-organization support, role-based permissions, MySQL database with Sequelize ORM, and comprehensive admin functionality. The backend follows MVC architecture with proper middleware, authentication, and database management.

## Technology Stack
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: MySQL with Sequelize ORM + TypeScript models
- **Authentication**: JWT with bcryptjs
- **File Handling**: Multer for multipart/form-data
- **Security**: Helmet, CORS, Morgan logging
- **Development**: Nodemon, ts-node
- **Architecture**: MVC pattern with middleware

## Code Style & Conventions

### TypeScript
- Use strict TypeScript configuration with decorators support
- Define interfaces for all request/response types
- Use proper typing for Express Request/Response objects
- Implement generic types for database models
- Use path aliases (@/*) for clean imports

### File Organization
```
src/
├── controllers/        # Route handlers and business logic
├── models/            # Sequelize TypeScript models
├── routes/            # Express route definitions
├── middleware/        # Custom middleware functions
├── services/          # Business logic and external integrations
├── utils/             # Utility functions and helpers
├── config/            # Configuration files (database, environment)
├── types/             # TypeScript type definitions
├── scripts/           # Database scripts and utilities
└── admin/             # Admin-specific functionality
    ├── controllers/   # Admin controllers
    ├── routes/        # Admin routes
    ├── middleware/    # Admin middleware
    └── services/      # Admin services
```

### Database Models (Sequelize)
- Use Sequelize TypeScript decorators
- Define proper model associations
- Implement validation rules in models
- Use consistent naming conventions (snake_case for DB, camelCase for JS)
- Include proper timestamps and soft deletes where needed

### Controllers
- Keep controllers thin, delegate business logic to services
- Use consistent error handling patterns
- Implement proper request validation
- Return consistent response formats
- Handle async operations properly with try-catch

### Routes
- Group related routes in separate files
- Use Express Router for modular routing
- Apply middleware at appropriate levels
- Implement proper HTTP status codes
- Use descriptive route names and parameters

### Middleware
- Create reusable middleware functions
- Implement authentication and authorization middleware
- Use organization-based middleware for multi-tenancy
- Handle errors consistently across middleware
- Log requests and responses appropriately

### API Response Format
```typescript
// Success Response
{
  success: true,
  data: any,
  message?: string
}

// Error Response
{
  success: false,
  error: string,
  message: string,
  details?: any
}
```

## Authentication & Authorization

### JWT Implementation
- Use secure JWT tokens with proper expiration
- Include user and organization information in tokens
- Implement token refresh mechanism
- Handle token validation in middleware
- Store sensitive data securely

### Role-Based Access Control
- Implement role and permission checking middleware
- Use organization-scoped permissions
- Check page access permissions
- Handle admin vs regular user access
- Implement proper authorization flows

### Multi-Organization Support
- Include organization_id in all relevant queries
- Use organization middleware for request scoping
- Handle organization switching properly
- Ensure data isolation between organizations
- Implement organization-based user management

## Database Patterns

### Sequelize Models
```typescript
@Table({
  tableName: 'table_name',
  timestamps: true,
  paranoid: true // for soft deletes
})
export class ModelName extends Model {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true
  })
  id!: string;

  // Define associations
  @BelongsTo(() => Organization)
  organization!: Organization;
}
```

### Database Transactions
- Use transactions for multi-table operations
- Implement proper rollback mechanisms
- Handle transaction errors appropriately
- Use managed transactions where possible

### Migrations
- Create sequential migration files
- Use descriptive migration names
- Include both up and down migrations
- Test migrations thoroughly
- Document schema changes

## API Development Patterns

### Controller Pattern
```typescript
export const controllerFunction = async (req: Request, res: Response) => {
  try {
    // Validate request
    const { param } = req.body;
    
    // Business logic (delegate to service)
    const result = await serviceFunction(param);
    
    // Return response
    res.status(200).json({
      success: true,
      data: result,
      message: 'Operation successful'
    });
  } catch (error) {
    console.error('Controller error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
};
```

### Service Pattern
```typescript
export const serviceFunction = async (param: any): Promise<any> => {
  // Implement business logic
  // Handle database operations
  // Return processed data
};
```

### Middleware Pattern
```typescript
export const middlewareFunction = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Middleware logic
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      error: 'Middleware error',
      message: error.message
    });
  }
};
```

## File Upload Handling
- Use Multer for multipart/form-data
- Implement proper file validation
- Handle file storage securely
- Limit file sizes appropriately
- Clean up temporary files

## Error Handling
- Implement global error handler
- Use consistent error response format
- Log errors with appropriate detail
- Handle different error types (validation, database, auth)
- Return user-friendly error messages

## Security Best Practices
- Use Helmet for security headers
- Implement proper CORS configuration
- Validate and sanitize all inputs
- Use parameterized queries to prevent SQL injection
- Hash passwords with bcryptjs
- Implement rate limiting where appropriate
- Use HTTPS in production

## Environment Configuration
- Use dotenv for environment variables
- Separate development/production configs
- Keep sensitive data in environment variables
- Validate required environment variables on startup
- Use different database configs per environment

## Database Best Practices
- Use connection pooling
- Implement proper indexing
- Use transactions for data consistency
- Handle connection errors gracefully
- Monitor database performance
- Use prepared statements

## API Documentation
- Document all endpoints with clear descriptions
- Include request/response examples
- Document authentication requirements
- Specify required permissions
- Include error response examples

## Testing Considerations
- Write unit tests for services and utilities
- Test API endpoints with proper mocking
- Test authentication and authorization flows
- Test database operations with test database
- Mock external dependencies

## Performance Optimization
- Use database indexes appropriately
- Implement query optimization
- Use connection pooling
- Cache frequently accessed data
- Optimize file upload handling
- Monitor API response times

## Common Patterns

### Organization Scoped Query
```typescript
const results = await Model.findAll({
  where: {
    organization_id: req.organizationId,
    // other conditions
  }
});
```

### Permission Check
```typescript
if (!req.user.permissions.includes('required_permission')) {
  return res.status(403).json({
    success: false,
    error: 'Insufficient permissions'
  });
}
```

### Transaction Pattern
```typescript
const transaction = await sequelize.transaction();
try {
  // Database operations
  await transaction.commit();
} catch (error) {
  await transaction.rollback();
  throw error;
}
```

## Best Practices
- Use TypeScript strictly with proper types
- Implement proper error handling everywhere
- Use middleware for cross-cutting concerns
- Keep controllers thin and services focused
- Use consistent naming conventions
- Document complex business logic
- Implement proper logging
- Handle edge cases gracefully
- Use environment-specific configurations
- Follow RESTful API conventions

## Avoid
- Hardcoded configuration values
- SQL injection vulnerabilities
- Missing error handling
- Blocking synchronous operations
- Exposing sensitive data in responses
- Missing input validation
- Inconsistent response formats
- Direct database queries in controllers
- Missing authentication checks
- Poorly structured code organization

## Admin Functionality
- Separate admin routes and controllers
- Implement admin-specific middleware
- Use proper admin authentication
- Handle organization management
- Implement user management features
- Provide system monitoring capabilities


