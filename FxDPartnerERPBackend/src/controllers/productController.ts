import { Request, Response } from 'express';
import { Transaction, Op } from 'sequelize';
import sequelize from '../config/sequelize';
import { Product, SKU, CreateProductData, UpdateProductData, CreateSKUData, UpdateSKUData } from '../models/Product';
import { Organization } from '../models/Organization';

// Get all products with SKUs
export const getProducts = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const products = await Product.findAll({
      where: { organization_id: organizationId },
      include: [
        {
          model: SKU,
          as: 'skus'
        },
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
};

// Get single product with SKUs
export const getProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const product = await Product.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: SKU,
          as: 'skus'
        },
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
};

// Create product
export const createProduct = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const productData: CreateProductData = {
      ...req.body,
      organization_id: organizationId
    };

    // Validate required fields
    if (!productData.name) {
      return res.status(400).json({
        error: 'Product name is required'
      });
    }

    // Check if product with same name already exists
    const existingProduct = await Product.findOne({
      where: { 
        name: productData.name,
        organization_id: organizationId 
      }
    });

    if (existingProduct) {
      return res.json(existingProduct);
    }

    // Validate organization exists
    const organization = await Organization.findByPk(organizationId);
    if (!organization) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    const product = await Product.create(productData as any);

    // Fetch the created product with associations
    const createdProduct = await Product.findByPk(product.id, {
      include: [
        {
          model: SKU,
          as: 'skus'
        },
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.status(201).json(createdProduct);
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
};

// Update product
export const updateProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const updateData: UpdateProductData = req.body;

    // Find product
    const product = await Product.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Update product
    await product.update(updateData);

    // Fetch the updated product with associations
    const updatedProduct = await Product.findByPk(id, {
      include: [
        {
          model: SKU,
          as: 'skus'
        },
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
};

// Delete product
export const deleteProduct = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find product
    const product = await Product.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      transaction
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Delete associated SKUs first
    await SKU.destroy({
      where: { product_id: id },
      transaction
    });

    // Delete product
    await product.destroy({ transaction });

    await transaction.commit();
    
    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
};

// SKU Management

// Get all SKUs for a product
export const getProductSKUs = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Verify product belongs to organization
    const product = await Product.findOne({
      where: { 
        id: productId,
        organization_id: organizationId 
      }
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const skus = await SKU.findAll({
      where: { product_id: productId },
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json(skus);
  } catch (error) {
    console.error('Error fetching SKUs:', error);
    res.status(500).json({ error: 'Failed to fetch SKUs' });
  }
};

// Create SKU
export const createSKU = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const skuData: CreateSKUData = {
      ...req.body,
      organization_id: organizationId
    };

    // Validate required fields
    if (!skuData.product_id || !skuData.code || !skuData.unit_type) {
      return res.status(400).json({ 
        error: 'Product ID, SKU code, and unit type are required' 
      });
    }

    // Verify product belongs to organization
    const product = await Product.findOne({
      where: { 
        id: skuData.product_id,
        organization_id: organizationId 
      }
    });

    if (!product) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }

    // Check if SKU with same code already exists
    const existingSKU = await SKU.findOne({
      where: { 
        code: skuData.code,
        organization_id: organizationId 
      }
    });

    if (existingSKU) {
      return res.json(existingSKU);
    }

    const sku = await SKU.create(skuData as any);

    // Fetch the created SKU with associations
    const createdSKU = await SKU.findByPk(sku.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        }
      ]
    });

    res.status(201).json(createdSKU);
  } catch (error) {
    console.error('Error creating SKU:', error);
    res.status(500).json({ error: 'Failed to create SKU' });
  }
};

// Update SKU
export const updateSKU = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const updateData: UpdateSKUData = req.body;

    // Find SKU and verify it belongs to organization
    const sku = await SKU.findOne({
      where: { id },
      include: [
        {
          model: Product,
          where: { organization_id: organizationId }
        }
      ]
    });

    if (!sku) {
      return res.status(404).json({ error: 'SKU not found' });
    }

    // Update SKU
    await sku.update(updateData);

    // Fetch the updated SKU with associations
    const updatedSKU = await SKU.findByPk(id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        }
      ]
    });

    res.json(updatedSKU);
  } catch (error) {
    console.error('Error updating SKU:', error);
    res.status(500).json({ error: 'Failed to update SKU' });
  }
};

// Delete SKU
export const deleteSKU = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find SKU and verify it belongs to organization
    const sku = await SKU.findOne({
      where: { id },
      include: [
        {
          model: Product,
          where: { organization_id: organizationId }
        }
      ]
    });

    if (!sku) {
      return res.status(404).json({ error: 'SKU not found' });
    }

    await sku.destroy();

    res.json({ message: 'SKU deleted successfully' });
  } catch (error) {
    console.error('Error deleting SKU:', error);
    res.status(500).json({ error: 'Failed to delete SKU' });
  }
};

// Get all products and SKUs for dropdown selection
export const getAllProductsAndSKUs = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const products = await Product.findAll({
      where: { 
        organization_id: organizationId,
        status: 'active'
      },
      include: [
        {
          model: SKU,
          as: 'skus',
          where: { status: 'active' },
          required: false
        }
      ],
      order: [['name', 'ASC'], [{ model: SKU, as: 'skus' }, 'code', 'ASC']]
    });

    res.json(products);
  } catch (error) {
    console.error('Error fetching products and SKUs:', error);
    res.status(500).json({ error: 'Failed to fetch products and SKUs' });
  }
};

// Search products (without SKUs)
export const searchProducts = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    // Find all products that match the search query (without SKUs)
    const matchingProducts = await Product.findAll({
      where: {
        organization_id: organizationId,
        [Op.or]: [
          { name: { [Op.like]: `%${q}%` } }
        ]
      },
      attributes: ['id', 'name', 'status'],
      limit: 20,
      order: [['name', 'ASC']]
    });

    res.json(matchingProducts);
  } catch (error) {
    console.error('Error searching products:', error);
    res.status(500).json({ error: 'Failed to search products' });
  }
};

// Search products with detailed SKU information
export const searchProductsWithSKUs = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    // Find all products that match the search query (without SKUs)
    const matchingProducts = await Product.findAll({
      where: {
        organization_id: organizationId,
        status: 'active',
        [Op.or]: [
          { name: { [Op.like]: `%${q}%` } }
        ]
      },
      attributes: ['id', 'name', 'description', 'status'],
      limit: 15,
      order: [['name', 'ASC']]
    });

    res.json(matchingProducts);
  } catch (error) {
    console.error('Error searching products with SKUs:', error);
    res.status(500).json({ error: 'Failed to search products with SKUs' });
  }
};

// Search SKUs by product ID and query
export const searchSKUs = async (req: Request, res: Response) => {
  try {
    const { productId, q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!productId || typeof productId !== 'string') {
      return res.status(400).json({ error: 'Product ID is required' });
    }

    // Build the where condition
    const whereCondition: any = {
      product_id: productId,
      status: 'active'
    };

    // Add search query if provided
    if (q && typeof q === 'string') {
      whereCondition.code = { [Op.like]: `%${q}%` };
    }

    // Find all SKUs that match the criteria
    const skus = await SKU.findAll({
      where: whereCondition,
      attributes: ['id', 'code', 'unit_type', 'unit_weight', 'status'],
      order: [['code', 'ASC']]
    });

    res.json(skus);
  } catch (error) {
    console.error('Error searching SKUs:', error);
    res.status(500).json({ error: 'Failed to search SKUs' });
  }
};

// Get recently used products (based on vehicle arrivals)
export const getRecentProducts = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Get products used in recent vehicle arrivals (last 30 days)
    const recentProducts = await Product.findAll({
      where: {
        organization_id: organizationId,
        status: 'active'
      },
      include: [
        {
          model: SKU,
          as: 'skus',
          where: { status: 'active' },
          required: false,
          attributes: ['id', 'code', 'unit_type', 'unit_weight', 'status']
        }
      ],
      attributes: ['id', 'name', 'description', 'status', 'updated_at'],
      limit: 10,
      order: [['updated_at', 'DESC'], [{ model: SKU, as: 'skus' }, 'code', 'ASC']]
    });

    res.json(recentProducts);
  } catch (error) {
    console.error('Error fetching recent products:', error);
    res.status(500).json({ error: 'Failed to fetch recent products' });
  }
};
