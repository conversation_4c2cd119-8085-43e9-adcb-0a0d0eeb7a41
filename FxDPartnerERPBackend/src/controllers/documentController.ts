import { Request, Response } from 'express';
import multer from 'multer';
import s3Service from '../services/s3Service';
import { Document, EntityType, CreateDocumentData } from '../models/Document';

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Maximum 10 files per request
  },
  fileFilter: (req, file, cb) => {
    // Allow common file types
    const allowedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed`));
    }
  }
});

// Middleware for single file upload
export const uploadSingle = upload.single('file');

// Middleware for multiple file upload
export const uploadMultiple = upload.array('files', 10);

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    organization_id: string;
  };
}

export class DocumentController {
  /**
   * Upload a single file and optionally attach to entity
   */
  static async uploadFile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.file) {
        res.status(400).json({ 
          success: false, 
          message: 'No file uploaded' 
        });
        return;
      }

      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message: 'S3 service not properly configured'
        });
        return;
      }

      // Upload file to S3
      const uploadResult = await s3Service.uploadFile(
        req.file.originalname,
        req.file.buffer,
        req.file.mimetype
      );

      // Check if entityType and entityId are provided for auto-attachment
      const { entityType, entityId } = req.body;
      
      if (entityType && entityId) {
        // Validate entity type
        if (!Object.values(EntityType).includes(entityType as EntityType)) {
          res.status(400).json({
            success: false,
            message: 'Invalid entity type'
          });
          return;
        }

        // Generate fresh file URL
        const fileUrl = await s3Service.getFileUrl(uploadResult.fileKey);

        // Create document record attached to entity
        const documentData: CreateDocumentData = {
          file_name: uploadResult.fileName,
          file_key: uploadResult.fileKey,
          file_url: fileUrl,
          file_size: uploadResult.fileSize,
          content_type: uploadResult.contentType,
          entity_type: entityType as EntityType,
          entity_id: entityId,
          display_name: req.body.displayName || uploadResult.fileName,
          organization_id: req.user.organization_id,
          uploaded_by: req.user.id
        };

        const document = await Document.create(documentData as any);

        res.status(200).json({
          success: true,
          data: {
            upload: uploadResult,
            document: document
          }
        });
      } else {
        // Just return upload result without attaching
        res.status(200).json({
          success: true,
          data: uploadResult
        });
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to upload file'
      });
    }
  }

  /**
   * Upload a single file to S3 (legacy method)
   */
  static async uploadFileOnly(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.file) {
        res.status(400).json({ 
          success: false, 
          message: 'No file uploaded' 
        });
        return;
      }

      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message: 'S3 service not properly configured'
        });
        return;
      }

      // Upload file to S3
      const uploadResult = await s3Service.uploadFile(
        req.file.originalname,
        req.file.buffer,
        req.file.mimetype
      );

      res.status(200).json({
        success: true,
        data: uploadResult
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to upload file'
      });
    }
  }

  /**
   * Upload multiple files to S3
   */
  static async uploadMultipleFiles(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length === 0) {
        res.status(400).json({ 
          success: false, 
          message: 'No files uploaded' 
        });
        return;
      }

      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message: 'S3 service not properly configured'
        });
        return;
      }

      // Prepare files for upload
      const filesToUpload = files.map(file => ({
        filename: file.originalname,
        buffer: file.buffer,
        contentType: file.mimetype
      }));

      // Upload files to S3
      const uploadResults = await s3Service.uploadMultipleFiles(filesToUpload);

      res.status(200).json({
        success: true,
        data: {
          files: uploadResults,
          count: uploadResults.length
        }
      });
    } catch (error) {
      console.error('Error uploading files:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to upload files'
      });
    }
  }

  /**
   * Attach a document to an entity
   */
  static async attachDocument(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { entityType, entityId } = req.params;
      const { fileKey, fileName, fileSize, contentType, displayName } = req.body;

      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      // Validate entity type
      if (!Object.values(EntityType).includes(entityType as EntityType)) {
        res.status(400).json({
          success: false,
          message: 'Invalid entity type'
        });
        return;
      }

      // Validate required fields
      if (!fileKey || !fileName || !fileSize || !contentType) {
        res.status(400).json({
          success: false,
          message: 'Missing required document information'
        });
        return;
      }

      // Generate fresh file URL
      const fileUrl = await s3Service.getFileUrl(fileKey);

      // Create document record
      const documentData: CreateDocumentData = {
        file_name: fileName,
        file_key: fileKey,
        file_url: fileUrl,
        file_size: fileSize,
        content_type: contentType,
        entity_type: entityType as EntityType,
        entity_id: entityId,
        display_name: displayName,
        organization_id: req.user.organization_id,
        uploaded_by: req.user.id
      };

      const document = await Document.create(documentData as any);

      res.status(201).json({
        success: true,
        data: document
      });
    } catch (error) {
      console.error('Error attaching document:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to attach document'
      });
    }
  }

  /**
   * Get documents for an entity
   */
  static async getEntityDocuments(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { entityType, entityId } = req.params;

      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      // Validate entity type
      if (!Object.values(EntityType).includes(entityType as EntityType)) {
        res.status(400).json({
          success: false,
          message: 'Invalid entity type'
        });
        return;
      }

      const documents = await Document.findByEntity(
        entityType as EntityType,
        entityId,
        req.user.organization_id
      );

      // Refresh file URLs for all documents
      for (const doc of documents) {
        if (doc.file_key) {
          doc.file_url = await s3Service.getFileUrl(doc.file_key);
        }
      }

      res.status(200).json({
        success: true,
        data: documents
      });
    } catch (error) {
      console.error('Error fetching entity documents:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch documents'
      });
    }
  }

  /**
   * Delete a document
   */
  static async deleteDocument(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const document = await Document.findOne({
        where: {
          id,
          organization_id: req.user.organization_id
        }
      });

      if (!document) {
        res.status(404).json({
          success: false,
          message: 'Document not found'
        });
        return;
      }

      // Delete file from S3
      await s3Service.deleteFile(document.file_key);

      // Delete document record
      await document.destroy();

      res.status(200).json({
        success: true,
        message: 'Document deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete document'
      });
    }
  }

  /**
   * Search documents
   */
  static async searchDocuments(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { q: searchTerm } = req.query;

      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      if (!searchTerm || typeof searchTerm !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Search term is required'
        });
        return;
      }

      const documents = await Document.searchByFileName(
        searchTerm,
        req.user.organization_id
      );

      // Refresh file URLs for all documents
      for (const doc of documents) {
        if (doc.file_key) {
          doc.file_url = await s3Service.getFileUrl(doc.file_key);
        }
      }

      res.status(200).json({
        success: true,
        data: documents
      });
    } catch (error) {
      console.error('Error searching documents:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to search documents'
      });
    }
  }

  /**
   * Get organization documents
   */
  static async getOrganizationDocuments(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { limit } = req.query;

      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
        return;
      }

      const documents = await Document.findByOrganization(
        req.user.organization_id,
        limit ? parseInt(limit as string) : undefined
      );

      // Refresh file URLs for all documents
      for (const doc of documents) {
        if (doc.file_key) {
          doc.file_url = await s3Service.getFileUrl(doc.file_key);
        }
      }

      res.status(200).json({
        success: true,
        data: documents
      });
    } catch (error) {
      console.error('Error fetching organization documents:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to fetch documents'
      });
    }
  }
}
