import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as crypto from 'crypto';

// S3 Configuration
const s3Config = {
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
  },
  region: process.env.AWS_REGION || 'ap-south-1',
  bucketName: process.env.NODE_ENV === 'production' ? 
    (process.env.AWS_BUCKET_NAME_PROD || 'fxd-erp-docs-prod') : 
    (process.env.AWS_S3_BUCKET_NAME || 'fxd-erp-docs-staging')
};

export interface FileUploadResult {
  fileKey: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  contentType: string;
}

class S3Service {
  private s3Client: S3Client;

  constructor() {
    this.s3Client = new S3Client({
      region: s3Config.region,
      credentials: s3Config.credentials
    });
  }

  /**
   * Generate a unique file key for S3
   */
  generateFileKey(filename: string): string {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(filename);
    const sanitizedName = path.basename(filename, extension)
      .replace(/[^a-z0-9]/gi, '-')
      .toLowerCase();
    
    return `${sanitizedName}-${timestamp}-${randomString}${extension}`;
  }

  /**
   * Upload a file buffer to S3
   */
  async uploadFile(
    filename: string, 
    buffer: Buffer, 
    contentType: string
  ): Promise<FileUploadResult> {
    try {
      const fileKey = this.generateFileKey(filename);

      const command = new PutObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey,
        Body: buffer,
        ContentType: contentType
      });

      await this.s3Client.send(command);
      console.log('File uploaded to S3 successfully:', fileKey);
      
      const fileUrl = await this.getFileUrl(fileKey);

      return {
        fileKey,
        fileUrl,
        fileName: filename,
        fileSize: buffer.length,
        contentType
      };
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload multiple files to S3
   */
  async uploadMultipleFiles(
    files: Array<{ filename: string; buffer: Buffer; contentType: string }>
  ): Promise<FileUploadResult[]> {
    try {
      const uploadPromises = files.map(file => 
        this.uploadFile(file.filename, file.buffer, file.contentType)
      );
      
      const results = await Promise.all(uploadPromises);
      return results;
    } catch (error) {
      console.error('Error uploading multiple files to S3:', error);
      throw new Error(`Failed to upload files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate a pre-signed URL for retrieving a file
   */
  async getFileUrl(fileKey: string, expiresIn: number = 86400): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      throw new Error(`Failed to generate file URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(fileKey: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey
      });

      await this.s3Client.send(command);
      console.log('File deleted from S3 successfully:', fileKey);
    } catch (error) {
      console.error('Error deleting file from S3:', error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if S3 service is properly configured
   */
  isConfigured(): boolean {
    return !!(
      s3Config.credentials.accessKeyId &&
      s3Config.credentials.secretAccessKey &&
      s3Config.region &&
      s3Config.bucketName
    );
  }

  /**
   * Get bucket name for current environment
   */
  getBucketName(): string {
    return s3Config.bucketName;
  }
}

// Create and export singleton instance
const s3Service = new S3Service();
export default s3Service;
