import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  HasMany,
  BelongsToMany,
} from 'sequelize-typescript';
import { User } from './User';
import { UserOrganization } from './UserOrganization';

@Table({
  tableName: 'organizations',
  timestamps: true,
  underscored: true,
})
export class Organization extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.STRING)
  code!: string;

  @Column(DataType.STRING)
  email?: string;

  @Column(DataType.STRING)
  phone?: string;

  @Column(DataType.TEXT)
  address?: string;

  @Column(DataType.STRING)
  gst_number?: string;

  @Column(DataType.STRING)
  pan_number?: string;

  @Column(DataType.STRING)
  owner_name?: string;

  @Default(0.0)
  @Column(DataType.DECIMAL(5, 2))
  default_commission_percentage!: number;

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive'))
  status!: string;

  @Default('basic')
  @Column(DataType.STRING)
  subscription_plan!: string;

  @Column(DataType.DATE)
  subscription_expires_at?: Date;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @HasMany(() => UserOrganization)
  user_organizations!: UserOrganization[];

  @BelongsToMany(() => User, () => UserOrganization)
  users!: User[];
}

// Export interfaces for backward compatibility
export interface CreateOrganizationData {
  name: string;
  code: string;
  email?: string;
  phone?: string;
  address?: string;
  gst_number?: string;
  pan_number?: string;
  owner_name?: string;
  default_commission_percentage?: number;
  subscription_plan?: string;
}

export interface UpdateOrganizationData {
  name?: string;
  code?: string;
  email?: string;
  phone?: string;
  address?: string;
  gst_number?: string;
  pan_number?: string;
  owner_name?: string;
  default_commission_percentage?: number;
  status?: 'active' | 'inactive';
  subscription_plan?: string;
  subscription_expires_at?: Date;
}
